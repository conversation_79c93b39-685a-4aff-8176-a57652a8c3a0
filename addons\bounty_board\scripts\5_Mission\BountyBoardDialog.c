class BountyBoardDialogMenu : UIScriptedMenu
{
    static const int MENU_BOUNTY_BOARD = 10001;

    protected Widget layoutRoot;
    protected ListBoxWidget playerList;

    override Widget Init()
    {
        layoutRoot = GetGame().GetWorkspace().CreateWidgets("bounty_board/gui/layouts/BountyBoardDialog.layout");
        if (!layoutRoot) return NULL;

        playerList = ListBoxWidget.Cast(layoutRoot.FindAnyWidget("PlayerList"));
        UpdatePlayerList();

        return layoutRoot;
    }

    void UpdatePlayerList()
    {
        if (!playerList) return;

        playerList.ClearItems();

        array<Man> players = new array<Man>;
        GetGame().GetPlayers(players);

        foreach (Man player : players)
        {
            PlayerBase pb = PlayerBase.Cast(player);
            if (pb && pb.GetIdentity())
            {
                string name = pb.GetIdentity().GetName();
                if (name != "")
                {
                    playerList.AddItem(name, NULL, 0);
                }
            }
        }
    }

    override bool OnClick(Widget w, int x, int y, int button)
    {
        // You can add button click handling here
        return super.OnClick(w, x, y, button);
    }
}
