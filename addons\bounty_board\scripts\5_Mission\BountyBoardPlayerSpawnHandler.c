class BountyBoardPlayerSpawnHandler
{
    void BountyBoardPlayerSpawnHandler()
    {
        GetGame().Event_OnClientReady.Insert(OnClientReady);
    }

    void ~BountyBoardPlayerSpawnHandler()
    {
        GetGame().Event_OnClientReady.Remove(OnClientReady);
    }

    void OnClientReady(PlayerBase player)
    {
        if (!GetGame().IsServer()) return;
        if (!player) return;

        vector playerPos = player.GetPosition();
        vector forward = player.GetDirection();
        vector spawnPos = playerPos + (forward * 3.0); // 3 meters in front

        BountyBoardNPC npc = BountyBoardNPC.Cast(GetGame().CreateObject("BountyBoardNPC", spawnPos, false, true));
        if (npc)
        {
            npc.SetOrientation(player.GetOrientation());
        }
    }
}

static ref BountyBoardPlayerSpawnHandler g_BountyBoardPlayerSpawnHandler = new BountyBoardPlayerSpawnHandler(); 