class BountyBoardNPC : SurvivorBase
{
    override void SetActions()
    {
        super.SetActions();
        AddAction(ActionOpenBountyBoard);
    }
};

class ActionOpenBountyBoard : ActionInteractBase
{
    void ActionOpenBountyBoard()
    {
        m_CommandUID = DayZPlayerConstants.CMD_ACTIONMOD_INTERACTONCE;
        m_StanceMask = DayZPlayerConstants.STANCEMASK_ALL;
        m_Text = "Open Bounty Board";
        m_MessageSuccess = "Opening Bounty Board...";
    }

    override string GetText()
    {
        return m_Text;
    }

    override bool ActionCondition(PlayerBase player, ActionTarget target, ItemBase item)
    {
        return true;
    }

    override void OnStartClient(ActionData action_data)
    {
        super.OnStartClient(action_data);
        if (GetGame() && GetGame().GetUIManager())
        {
            GetGame().GetUIManager().ShowScriptedMenu(BountyBoardDialogMenu.MENU_BOUNTY_BOARD, NULL);
        }
    }
};